/**
 * ZUSTAND PLACEHOLDER
 * Este é um placeholder para a biblioteca Zustand.
 * Para usar o sistema completo, baixe a biblioteca oficial de:
 * https://unpkg.com/zustand@4.4.1/esm/index.js
 * 
 * Ou use o CDN:
 * https://unpkg.com/zustand@4.4.1/index.umd.js
 */

// Implementação básica para desenvolvimento
window.zustand = {
    create: (createState) => {
        let state = {};
        let listeners = [];
        
        const setState = (partial) => {
            const newState = typeof partial === 'function' ? partial(state) : partial;
            state = { ...state, ...newState };
            listeners.forEach(listener => listener(state));
        };
        
        const getState = () => state;
        
        const subscribe = (listener) => {
            listeners.push(listener);
            return () => {
                listeners = listeners.filter(l => l !== listener);
            };
        };
        
        const api = { setState, getState, subscribe };
        state = createState(setState, getState, api);
        
        return api;
    }
};

console.log('📦 Zustand placeholder carregado - Para produção, use a biblioteca oficial');
