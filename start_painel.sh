#!/bin/bash

echo "========================================"
echo "      LILITH ENTERPRISE - PAINEL"
echo "========================================"

# Configurar PYTHONPATH para incluir dependências
export PYTHONPATH="/home/<USER>/.local/lib/python3.12/site-packages:/usr/lib/python3/dist-packages:$PYTHONPATH"

# Verificar se arquivo .env existe
if [ ! -f ".env" ]; then
    echo "⚠️  Arquivo .env não encontrado!"
    echo "📋 Copiando .env.example para .env..."
    cp .env.example .env
    echo "✏️  Edite o arquivo .env com suas chaves de API antes de usar as IAs externas"
    echo ""
fi

echo "🚀 Iniciando Painel Lilith..."
echo "🌐 Acesse: http://localhost:8000"
echo "⏹️  Para parar: Ctrl+C"
echo ""

# Iniciar o painel
python3 painel/painel.py
