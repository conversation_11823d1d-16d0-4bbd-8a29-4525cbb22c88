# LILITH ENTERPRISE - DEPENDÊNCIAS PRINCIPAIS
# Baseado na análise do código existente

# Framework web principal
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# HTTP e requests
requests>=2.31.0
httpx>=0.25.0

# Variáveis de ambiente
python-dotenv>=1.0.0

# APIs de IA
anthropic>=0.25.0

# CORS e middleware
python-multipart>=0.0.6

# Utilitários
typing-extensions>=4.7.0

# Opcional - para funcionalidades avançadas
# psycopg2-binary>=2.9.0  # PostgreSQL (descomente se necessário)
# asyncpg>=0.29.0         # PostgreSQL async (descomente se necessário)
